<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Modern Color Palette */
            --primary-50: #f0f9ff;
            --primary-100: #e0f2fe;
            --primary-200: #bae6fd;
            --primary-300: #7dd3fc;
            --primary-400: #38bdf8;
            --primary-500: #0ea5e9;
            --primary-600: #0284c7;
            --primary-700: #0369a1;
            --primary-800: #075985;
            --primary-900: #0c4a6e;

            /* Secondary Colors */
            --secondary-50: #fdf4ff;
            --secondary-100: #fae8ff;
            --secondary-200: #f5d0fe;
            --secondary-300: #f0abfc;
            --secondary-400: #e879f9;
            --secondary-500: #d946ef;
            --secondary-600: #c026d3;
            --secondary-700: #a21caf;
            --secondary-800: #86198f;
            --secondary-900: #701a75;

            /* Neutral Colors */
            --neutral-50: #fafafa;
            --neutral-100: #f5f5f5;
            --neutral-200: #e5e5e5;
            --neutral-300: #d4d4d4;
            --neutral-400: #a3a3a3;
            --neutral-500: #737373;
            --neutral-600: #525252;
            --neutral-700: #404040;
            --neutral-800: #262626;
            --neutral-900: #171717;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 50%, var(--primary-800) 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: var(--neutral-800);
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Landing page styles */
        .landing-page {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            position: relative;
            z-index: 2;
        }

        .landing-container {
            text-align: center;
            padding: 3rem 2rem;
            position: relative;
        }

        .landing-container::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120%;
            height: 120%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: -1;
        }

        .website-name {
            font-size: clamp(2.5rem, 8vw, 5rem);
            font-weight: 700;
            color: white;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            letter-spacing: -0.02em;
            text-shadow:
                0 4px 20px rgba(0, 0, 0, 0.3),
                0 0 40px rgba(255, 255, 255, 0.1);
            user-select: none;
            padding: 1.5rem 3rem;
            border-radius: 24px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .website-name::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .website-name:hover {
            transform: translateY(-8px) scale(1.02);
            text-shadow:
                0 8px 30px rgba(0, 0, 0, 0.4),
                0 0 60px rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.2),
                0 0 80px rgba(255, 255, 255, 0.1);
        }

        .website-name:hover::before {
            left: 100%;
        }

        .website-name:active {
            transform: translateY(-4px) scale(0.98);
        }

        .landing-subtitle {
            margin-top: 2rem;
            font-size: 1.25rem;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.9);
            letter-spacing: 0.5px;
            opacity: 0;
            animation: fadeInUp 1s ease 0.5s forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Clean Modern Header Styles */
        .header-modern {
            backdrop-filter: blur(8px);
            transition: all 0.2s ease;
        }

        /* Fix gradient logo */
        .logo-gradient {
            background: linear-gradient(135deg, #2563eb, #9333ea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            font-size: 1.5rem;
        }

        /* Modern header fix */
        .modern-header-fixed {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }



        /* Main app styles */
        .main-app {
            background: var(--neutral-50);
            min-height: 100vh;
            position: relative;
        }

        .main-app::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 10% 20%, var(--primary-50) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, var(--secondary-50) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
            opacity: 0.6;
        }

        .lucide {
            width: 20px;
            height: 20px;
        }

        /* Simple spinner for loading states */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4f46e5;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 400px;
            width: 100%;
            position: relative;
            transform: translateY(-50px);
            transition: transform 0.3s ease;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        .modal-overlay.active .modal-content {
            transform: translateY(0);
        }

        .modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            transition: color 0.2s ease;
        }

        .modal-close:hover {
            color: #333;
        }

        .modal-tabs {
            display: flex;
            margin-bottom: 2rem;
            border-bottom: 1px solid #eee;
        }

        .modal-tab {
            flex: 1;
            padding: 1rem;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }

        .modal-tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .form-container {
            display: none;
        }

        .form-container.active {
            display: block;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
            background: #fafafa;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
        }

        .form-button {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 1rem;
        }

        .form-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .form-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: none;
        }

        .success-message {
            color: #27ae60;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: none;
        }

        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.75rem;
        }

        .strength-weak { color: #e74c3c; }
        .strength-medium { color: #f39c12; }
        .strength-strong { color: #27ae60; }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff40;
            border-top: 2px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none !important;
        }

        /* Main app specific styles */
        .pull-to-refresh {
            position: relative;
        }

        /* Mobile navigation styles */
        .mobile-nav-item {
            transition: all 0.2s ease;
        }

        .mobile-nav-item.active {
            color: #4f46e5;
        }

        .mobile-nav-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        /* Adjust main content for mobile bottom nav */
        @media (max-width: 768px) {
            .landing-container {
                padding: 2rem 1.5rem;
            }

            .landing-subtitle {
                font-size: 1.1rem;
                margin-top: 1.5rem;
            }

            .modal-content {
                padding: 1.5rem;
                margin: 1rem;
            }

            #mainApp {
                padding-bottom: 80px;
            }

            /* Improved touch targets for mobile */
            button, .mobile-nav-item, input, textarea, select {
                min-height: 44px; /* Apple's recommended minimum touch target */
            }
        }

        @media (max-width: 480px) {
            .landing-container {
                padding: 1.5rem 1rem;
            }

            .landing-subtitle {
                font-size: 1rem;
                margin-top: 1rem;
            }

            .website-name {
                padding: 1rem 2rem;
                border-radius: 20px;
            }
        }

        /* Touch feedback */
        .touch-feedback {
            position: relative;
            overflow: hidden;
        }

        .touch-ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Post swipe actions */
        .post-swipe-container {
            position: relative;
        }

        .post-swipe-actions {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 0.5rem;
            padding: 0.5rem;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .post-swipe-actions.visible {
            opacity: 1;
            visibility: visible;
        }

        .swipe-action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .swipe-action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <!-- Landing Page -->
    <div id="landingPage" class="landing-page">
        <div class="landing-container">
            <h1 class="website-name" onclick="openAuthModal()">Naroop</h1>
            <p class="landing-subtitle">Narrative of Our People</p>
        </div>
    </div>

    <!-- Main App View (for authenticated users) -->
    <div id="mainApp" class="main-app hidden">
        <!-- Header -->
        <header class="modern-header-fixed fixed top-0 left-0 right-0 z-20">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between h-16">
                    <!-- Logo -->
                    <div class="flex items-center space-x-4">
                        <h1 class="logo-gradient">Naroop</h1>
                    </div>

                    <!-- Desktop Search -->
                    <div class="hidden md:flex flex-1 max-w-md mx-8">
                        <div class="relative w-full">
                            <input type="text" id="searchInput" placeholder="Search stories, people..."
                                   class="w-full pl-10 pr-4 py-2.5 bg-slate-100 border-0 rounded-xl focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all duration-200">
                            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4"></i>
                        </div>
                        <div id="searchResults" class="absolute top-full left-0 right-0 bg-white border border-slate-200 rounded-lg shadow-lg mt-1 hidden z-30 max-h-96 overflow-y-auto"></div>
                    </div>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex items-center space-x-3">
                        <!-- Notifications -->
                        <div class="relative">
                            <button id="notificationBtn" class="p-2.5 text-slate-600 hover:text-blue-600 hover:bg-slate-100 rounded-xl transition-all duration-200 relative">
                                <i data-lucide="bell" class="w-5 h-5"></i>
                                <span id="notificationBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
                            </button>
                            <div id="notificationDropdown" class="absolute right-0 mt-2 w-80 bg-white rounded-xl shadow-lg border border-slate-200 hidden z-30">
                                <div class="p-4 border-b border-slate-200">
                                    <h3 class="font-semibold text-slate-800">Notifications</h3>
                                </div>
                                <div id="notificationsList" class="max-h-64 overflow-y-auto">
                                    <div class="p-4 text-center text-slate-500">
                                        <i data-lucide="bell" class="w-8 h-8 mx-auto mb-2 text-slate-300"></i>
                                        <p>No notifications yet</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Messages -->
                        <button id="messagesBtn" class="p-2.5 text-slate-600 hover:text-blue-600 hover:bg-slate-100 rounded-xl transition-all duration-200">
                            <i data-lucide="message-circle" class="w-5 h-5"></i>
                        </button>

                        <!-- User Menu -->
                        <div class="relative">
                            <button id="userMenuBtn" class="flex items-center space-x-2 p-2 text-slate-600 hover:text-blue-600 hover:bg-slate-100 rounded-xl transition-all duration-200">
                                <img id="userAvatar" src="https://placehold.co/32x32/E2E8F0/475569?text=G" alt="User" class="w-8 h-8 rounded-full">
                                <i data-lucide="chevron-down" class="w-4 h-4"></i>
                            </button>
                            <div id="userDropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 hidden z-30">
                                <div class="p-2">
                                    <button onclick="showProfile()" class="w-full text-left px-3 py-2 text-slate-700 hover:bg-slate-100 rounded-lg">Profile</button>
                                    <button onclick="showSettings()" class="w-full text-left px-3 py-2 text-slate-700 hover:bg-slate-100 rounded-lg">Settings</button>
                                    <hr class="my-2">
                                    <button onclick="signOut()" class="w-full text-left px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg">Sign Out</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button id="mobileMenuBtn" class="md:hidden p-2 text-slate-600 hover:text-blue-600 hover:bg-slate-100 rounded-xl transition-all duration-200">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content Grid -->
        <main class="container mx-auto px-4 pt-24 pb-10 relative z-10">
            <!-- Grid updated for better responsiveness -->
            <div class="grid grid-cols-1 md:grid-cols-10 lg:grid-cols-12 gap-8">

                <!-- Left Sidebar - Story Creation (Hidden on mobile) -->
                <aside class="hidden md:block md:col-span-3 lg:col-span-3">
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 sticky top-24">
                        <h2 class="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                            <i data-lucide="edit-3" class="w-5 h-5 text-indigo-600"></i>
                            Share Your Story
                        </h2>

                        <form id="storyForm" class="space-y-4">
                            <div>
                                <input type="text" id="storyTitleInput" placeholder="What's your story about?"
                                       class="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                            </div>

                            <div class="relative">
                                <textarea id="storyContentInput" placeholder="Share something positive that happened to you..."
                                          class="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none"
                                          rows="4"></textarea>
                                <button type="button" id="suggestTitlesBtn" class="hidden absolute bottom-2 right-2 bg-indigo-600 text-white px-3 py-1 rounded-lg text-sm hover:bg-indigo-700 transition-colors">
                                    ✨ Suggest Titles
                                </button>
                            </div>

                            <div id="titleSuggestionsContainer" class="hidden space-y-2"></div>

                            <button type="submit" id="postBtn" class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition-colors">
                                Share Story
                            </button>
                        </form>
                    </div>
                </aside>

                <!-- Main Feed -->
                <div class="md:col-span-7 lg:col-span-6 space-y-6 pull-to-refresh" id="mainFeed">
                    <!-- Pull to refresh indicator -->
                    <div id="pullRefreshIndicator" class="fixed top-20 left-1/2 transform -translate-x-1/2 bg-white rounded-full p-3 shadow-lg opacity-0 transition-all duration-300 z-30">
                        <i data-lucide="refresh-cw" class="w-6 h-6 text-indigo-600"></i>
                    </div>

                    <!-- Mobile Story Creation -->
                    <div class="md:hidden bg-white rounded-xl shadow-sm border border-slate-200 p-4">
                        <h2 class="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                            <i data-lucide="edit-3" class="w-5 h-5 text-indigo-600"></i>
                            Share Your Story
                        </h2>

                        <form id="mobileStoryForm" class="space-y-4">
                            <div>
                                <input type="text" id="mobileStoryTitleInput" placeholder="What's your story about?"
                                       class="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                            </div>

                            <div>
                                <textarea id="mobileStoryContentInput" placeholder="Share something positive..."
                                          class="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none"
                                          rows="3"></textarea>
                            </div>

                            <button type="submit" id="mobilePostBtn" class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition-colors">
                                Share Story
                            </button>
                        </form>
                    </div>

                    <!-- Feed Header -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-xl font-bold text-slate-800 flex items-center gap-2">
                                <i data-lucide="layout-grid"></i> Feed
                            </h2>
                            <button id="refreshFeedBtn" class="text-indigo-600 hover:text-indigo-700 p-2 hover:bg-indigo-50 rounded-lg transition-colors">
                                <i data-lucide="refresh-cw" class="w-5 h-5"></i>
                            </button>
                        </div>
                        <p class="text-slate-600">
                            Discover uplifting stories from our community. Our curated feed highlights positive experiences that motivate and celebrate our people.
                        </p>
                    </div>

                    <!-- Posts will be dynamically loaded here -->
                    <div id="postsContainer">
                        <!-- Loading skeleton will appear here initially -->
                    </div>

                    <!-- Load More Button -->
                    <div class="text-center">
                        <button id="loadMoreBtn" class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-6 py-3 rounded-lg transition-colors">
                            Load More Stories
                        </button>
                    </div>
                </div>

                <!-- Right Sidebar - User Info & Suggestions -->
                <aside class="hidden lg:block lg:col-span-3">
                    <div class="space-y-6 sticky top-24">
                        <!-- User Profile Card -->
                        <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                            <div class="flex items-center space-x-4 mb-4">
                                <img id="sidebarUserAvatar" src="https://placehold.co/48x48/E2E8F0/475569?text=G" alt="User" class="w-12 h-12 rounded-full">
                                <div>
                                    <h2 id="sidebarUsername" class="font-semibold text-slate-800">John D</h2>
                                    <p class="text-sm text-slate-500">Community Member</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-center">
                                <div>
                                    <div class="font-semibold text-slate-800">0</div>
                                    <div class="text-xs text-slate-500">Stories</div>
                                </div>
                                <div>
                                    <div class="font-semibold text-slate-800">0</div>
                                    <div class="text-xs text-slate-500">Following</div>
                                </div>
                                <div>
                                    <div class="font-semibold text-slate-800">0</div>
                                    <div class="text-xs text-slate-500">Followers</div>
                                </div>
                            </div>
                        </div>

                        <!-- People You May Know -->
                        <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                            <h3 class="font-semibold text-slate-800 mb-4">People You May Know</h3>
                            <div id="suggestedUsers" class="space-y-4">
                                <!-- Suggested users will be loaded here -->
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                            <h3 class="font-semibold text-slate-800 mb-4">Recent Activity</h3>
                            <div id="recentActivity" class="space-y-3">
                                <p class="text-slate-400 text-xs mt-1">Activity will appear here as you interact with stories</p>
                            </div>
                        </div>
                    </div>
                </aside>
            </div>
        </main>

        <!-- Mobile Search Modal -->
        <div id="mobileSearchModal" class="fixed inset-0 bg-white z-40 hidden md:hidden">
            <div class="flex flex-col h-full">
                <div class="flex items-center p-4 border-b border-slate-200">
                    <button onclick="closeMobileSearch()" class="mr-4 p-2 hover:bg-slate-100 rounded-lg">
                        <i data-lucide="arrow-left" class="w-5 h-5"></i>
                    </button>
                    <div class="flex-1 relative">
                        <input type="text" id="mobileSearchInput" placeholder="Search stories, people..."
                               class="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4"></i>
                    </div>
                </div>
                <div class="flex-1 overflow-y-auto p-4">
                    <div id="mobileSearchResults">
                        <!-- Mobile search results will appear here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav id="mobileBottomNav" class="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-slate-200 z-30">
        <div class="flex justify-around items-center py-2">
            <!-- Home Tab -->
            <button class="mobile-nav-item active flex flex-col items-center py-2 px-3 rounded-lg" data-tab="home">
                <div class="nav-icon relative">
                    <i data-lucide="home" class="w-6 h-6"></i>
                </div>
                <span class="text-xs mt-1 font-medium">Home</span>
            </button>

            <!-- Search Tab -->
            <button class="mobile-nav-item flex flex-col items-center py-2 px-3 rounded-lg" data-tab="search">
                <div class="nav-icon relative">
                    <i data-lucide="search" class="w-6 h-6"></i>
                </div>
                <span class="text-xs mt-1 font-medium">Search</span>
            </button>

            <!-- Create Tab -->
            <button class="mobile-nav-item flex flex-col items-center py-2 px-3 rounded-lg" data-tab="create">
                <div class="nav-icon relative">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                </div>
                <span class="text-xs mt-1 font-medium">Create</span>
            </button>

            <!-- Notifications Tab -->
            <button class="mobile-nav-item flex flex-col items-center py-2 px-3 rounded-lg" data-tab="notifications">
                <div class="nav-icon relative">
                    <i data-lucide="bell" class="w-6 h-6"></i>
                    <span id="mobileNotificationBadge" class="mobile-nav-badge hidden">0</span>
                </div>
                <span class="text-xs mt-1 font-medium">Alerts</span>
            </button>

            <!-- Profile Tab -->
            <button class="mobile-nav-item flex flex-col items-center py-2 px-3 rounded-lg" data-tab="profile">
                <div class="nav-icon relative">
                    <i data-lucide="user" class="w-6 h-6"></i>
                </div>
                <span class="text-xs mt-1 font-medium">Profile</span>
            </button>
        </div>
    </nav>

    <!-- Authentication Modal -->
    <div id="authModal" class="modal-overlay">
        <div class="modal-content">
            <button class="modal-close" onclick="closeAuthModal()">&times;</button>

            <div class="modal-tabs">
                <button class="modal-tab active" onclick="showSignIn()">Sign In</button>
                <button class="modal-tab" onclick="showSignUp()">Sign Up</button>
            </div>

            <!-- Sign In Form -->
            <div id="signInForm" class="form-container active">
                <form onsubmit="handleSignIn(event)">
                    <div class="form-group">
                        <label class="form-label" for="signInEmail">Email</label>
                        <input type="email" id="signInEmail" class="form-input" required>
                        <div id="signInEmailError" class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="signInPassword">Password</label>
                        <input type="password" id="signInPassword" class="form-input" required>
                        <div id="signInPasswordError" class="error-message"></div>
                    </div>
                    <button type="submit" id="signInButton" class="form-button">
                        <span id="signInButtonText">Sign In</span>
                        <div id="signInLoading" class="loading hidden"></div>
                    </button>
                    <div id="signInError" class="error-message"></div>
                    <div id="signInSuccess" class="success-message"></div>
                </form>
            </div>

            <!-- Sign Up Form -->
            <div id="signUpForm" class="form-container">
                <form onsubmit="handleSignUp(event)">
                    <div class="form-group">
                        <label class="form-label" for="signUpEmail">Email</label>
                        <input type="email" id="signUpEmail" class="form-input" required>
                        <div id="signUpEmailError" class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="signUpPassword">Password</label>
                        <input type="password" id="signUpPassword" class="form-input" required oninput="checkPasswordStrength()">
                        <div id="passwordStrength" class="password-strength"></div>
                        <div id="signUpPasswordError" class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="confirmPassword">Confirm Password</label>
                        <input type="password" id="confirmPassword" class="form-input" required oninput="checkPasswordMatch()">
                        <div id="confirmPasswordError" class="error-message"></div>
                    </div>
                    <button type="submit" id="signUpButton" class="form-button">
                        <span id="signUpButtonText">Create Account</span>
                        <div id="signUpLoading" class="loading hidden"></div>
                    </button>
                    <div id="signUpError" class="error-message"></div>
                    <div id="signUpSuccess" class="success-message"></div>
                </form>
            </div>
        </div>
    </div>

    <!-- Comment Modal -->
    <div id="commentModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h2 id="modalStoryTitle" class="text-xl font-bold text-slate-800">Story Comments</h2>
                    <button id="closeModalBtn" class="text-slate-400 hover:text-slate-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <div class="flex-1 overflow-y-auto max-h-96 p-6">
                    <div id="existingComments">
                        <!-- Existing comments will be loaded here -->
                    </div>
                </div>

                <div class="border-t border-slate-200 p-6">
                    <div class="space-y-4">
                        <div class="flex gap-3">
                            <textarea id="commentTextarea" placeholder="Share your thoughts..."
                                      class="flex-1 p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none"
                                      rows="3"></textarea>
                            <button id="generateCommentBtn" class="bg-indigo-100 text-indigo-700 px-4 py-2 rounded-lg hover:bg-indigo-200 transition-colors">
                                ✨ Generate Comment
                            </button>
                        </div>
                        <div class="flex justify-between items-center">
                            <div id="commentError" class="text-red-500 text-sm"></div>
                            <button id="postCommentBtn" class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                                Post Comment
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages Modal -->
    <div id="messagesModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-xl w-full max-w-4xl h-[80vh] overflow-hidden">
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h2 class="text-xl font-bold text-slate-800">Messages</h2>
                    <button onclick="closeMessagesModal()" class="text-slate-400 hover:text-slate-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <div class="flex h-full">
                    <div class="w-1/3 border-r border-slate-200 overflow-y-auto">
                        <div id="conversationsList" class="p-4">
                            <!-- Conversations will be loaded here -->
                        </div>
                    </div>
                    <div class="flex-1 flex flex-col">
                        <div id="messageArea" class="flex-1 p-6 overflow-y-auto">
                            <div class="text-center text-slate-500 mt-20">
                                <i data-lucide="message-circle" class="w-12 h-12 mx-auto mb-4 text-slate-300"></i>
                                <p>Select a conversation to start messaging</p>
                            </div>
                        </div>
                        <div id="messageInput" class="border-t border-slate-200 p-4 hidden">
                            <div class="flex gap-3">
                                <input type="text" id="messageText" placeholder="Type a message..."
                                       class="flex-1 p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                                <button id="sendMessageBtn" class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors">
                                    Send
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module" src="/public/js/firebase-config.js"></script>
    <script>
        // Modern header scroll effect
        let lastScrollY = window.scrollY;
        const header = document.querySelector('.modern-header');

        function updateHeaderOnScroll() {
            const currentScrollY = window.scrollY;

            if (currentScrollY > 10) {
                header?.classList.add('header-scrolled');
            } else {
                header?.classList.remove('header-scrolled');
            }

            lastScrollY = currentScrollY;
        }

        // Throttled scroll listener for better performance
        let ticking = false;
        function handleScroll() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    updateHeaderOnScroll();
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', handleScroll, { passive: true });

        // Enhanced dropdown interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Add modern dropdown classes
            const dropdowns = document.querySelectorAll('#notificationDropdown, #userDropdown');
            dropdowns.forEach(dropdown => {
                dropdown.classList.add('modern-dropdown');
            });

            // Add dropdown header styling
            const notificationDropdown = document.getElementById('notificationDropdown');
            if (notificationDropdown) {
                const header = notificationDropdown.querySelector('.p-4.border-b');
                if (header) {
                    header.classList.add('dropdown-header');
                }
            }

            // Add dropdown item styling
            const dropdownItems = document.querySelectorAll('#userDropdown button');
            dropdownItems.forEach(item => {
                item.classList.add('dropdown-item');
                if (item.textContent.includes('Sign Out')) {
                    item.classList.add('danger');
                }
            });

            // Add search results styling
            const searchResults = document.getElementById('searchResults');
            if (searchResults) {
                searchResults.classList.add('search-results');
            }
        });
    </script>
    <script>
        // Modal functionality
        function openAuthModal() {
            const modal = document.getElementById('authModal');
            if (modal) {
                modal.classList.add('active');
            }
        }

        // Make function globally available
        window.openAuthModal = openAuthModal;

        function closeAuthModal() {
            document.getElementById('authModal').classList.remove('active');
            clearAllErrors();
        }

        function showSignIn() {
            document.getElementById('signInForm').classList.add('active');
            document.getElementById('signUpForm').classList.remove('active');
            document.querySelectorAll('.modal-tab')[0].classList.add('active');
            document.querySelectorAll('.modal-tab')[1].classList.remove('active');
            clearAllErrors();
        }

        function showSignUp() {
            document.getElementById('signInForm').classList.remove('active');
            document.getElementById('signUpForm').classList.add('active');
            document.querySelectorAll('.modal-tab')[0].classList.remove('active');
            document.querySelectorAll('.modal-tab')[1].classList.add('active');
            clearAllErrors();
        }

        function clearAllErrors() {
            const errorElements = document.querySelectorAll('.error-message, .success-message');
            errorElements.forEach(el => {
                el.style.display = 'none';
                el.textContent = '';
            });
        }

        // Close modal when clicking outside
        document.getElementById('authModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAuthModal();
            }
        });

        // Password strength checker
        function checkPasswordStrength() {
            const password = document.getElementById('signUpPassword').value;
            const strengthElement = document.getElementById('passwordStrength');

            if (password.length === 0) {
                strengthElement.textContent = '';
                return;
            }

            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            if (strength < 3) {
                strengthElement.textContent = 'Weak password';
                strengthElement.className = 'password-strength strength-weak';
            } else if (strength < 5) {
                strengthElement.textContent = 'Medium password';
                strengthElement.className = 'password-strength strength-medium';
            } else {
                strengthElement.textContent = 'Strong password';
                strengthElement.className = 'password-strength strength-strong';
            }
        }

        // Password match checker
        function checkPasswordMatch() {
            const password = document.getElementById('signUpPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const errorElement = document.getElementById('confirmPasswordError');

            if (confirmPassword.length === 0) {
                errorElement.style.display = 'none';
                return;
            }

            if (password !== confirmPassword) {
                errorElement.textContent = 'Passwords do not match';
                errorElement.style.display = 'block';
            } else {
                errorElement.style.display = 'none';
            }
        }

        // Form validation
        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.style.display = 'block';
        }

        function showSuccess(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.style.display = 'block';
        }

        function setLoading(buttonId, loadingId, textId, isLoading) {
            const button = document.getElementById(buttonId);
            const loading = document.getElementById(loadingId);
            const text = document.getElementById(textId);

            if (isLoading) {
                button.disabled = true;
                text.style.display = 'none';
                loading.classList.remove('hidden');
            } else {
                button.disabled = false;
                text.style.display = 'inline';
                loading.classList.add('hidden');
            }
        }

        // Sign In Handler
        async function handleSignIn(event) {
            event.preventDefault();
            clearAllErrors();

            const email = document.getElementById('signInEmail').value.trim();
            const password = document.getElementById('signInPassword').value;

            // Client-side validation
            if (!validateEmail(email)) {
                showError('signInEmailError', 'Please enter a valid email address');
                return;
            }

            if (password.length < 6) {
                showError('signInPasswordError', 'Password must be at least 6 characters');
                return;
            }

            setLoading('signInButton', 'signInLoading', 'signInButtonText', true);

            try {
                // Wait for Firebase to be available
                if (typeof window.FirebaseAuth === 'undefined') {
                    throw new Error('Firebase authentication is not available');
                }

                const result = await window.FirebaseAuth.signIn(email, password);

                if (result.success) {
                    showSuccess('signInSuccess', 'Sign in successful! Redirecting...');
                    setTimeout(() => {
                        window.location.reload(); // This will trigger the auth state change
                    }, 1000);
                } else {
                    showError('signInError', result.error || 'Sign in failed');
                }
            } catch (error) {
                console.error('Sign in error:', error);
                showError('signInError', 'Sign in failed. Please try again.');
            } finally {
                setLoading('signInButton', 'signInLoading', 'signInButtonText', false);
            }
        }

        // Sign Up Handler
        async function handleSignUp(event) {
            event.preventDefault();
            clearAllErrors();

            const email = document.getElementById('signUpEmail').value.trim();
            const password = document.getElementById('signUpPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // Client-side validation
            if (!validateEmail(email)) {
                showError('signUpEmailError', 'Please enter a valid email address');
                return;
            }

            if (password.length < 6) {
                showError('signUpPasswordError', 'Password must be at least 6 characters');
                return;
            }

            if (password !== confirmPassword) {
                showError('confirmPasswordError', 'Passwords do not match');
                return;
            }

            setLoading('signUpButton', 'signUpLoading', 'signUpButtonText', true);

            try {
                // Wait for Firebase to be available
                if (typeof window.FirebaseAuth === 'undefined') {
                    throw new Error('Firebase authentication is not available');
                }

                const username = email.split('@')[0]; // Use email prefix as username
                const result = await window.FirebaseAuth.signUp(email, password, username);

                if (result.success) {
                    showSuccess('signUpSuccess', 'Account created successfully! Redirecting...');
                    setTimeout(() => {
                        window.location.reload(); // This will trigger the auth state change
                    }, 1000);
                } else {
                    showError('signUpError', result.error || 'Account creation failed');
                }
            } catch (error) {
                console.error('Sign up error:', error);
                showError('signUpError', 'Account creation failed. Please try again.');
            } finally {
                setLoading('signUpButton', 'signUpLoading', 'signUpButtonText', false);
            }
        }

        // Global variables
        let currentUser = null;

        // --- View Management ---
        function showLandingPage() {
            document.getElementById('landingPage').classList.remove('hidden');
            document.getElementById('mainApp').classList.add('hidden');
            updateMobileNavVisibility();
        }

        function showMainApp() {
            document.getElementById('landingPage').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            updateMobileNavVisibility();

            // Initialize Lucide icons for the main app
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }

        // --- User Session Management ---
        function updateUIForUser() {
            if (!currentUser) return;

            const userAvatar = document.getElementById('userAvatar');
            const sidebarUserAvatar = document.getElementById('sidebarUserAvatar');
            const sidebarUsername = document.getElementById('sidebarUsername');

            if (userAvatar) {
                userAvatar.src = `https://placehold.co/32x32/E2E8F0/475569?text=${currentUser.username.substring(0, 2).toUpperCase()}`;
                userAvatar.alt = currentUser.username;
            }

            if (sidebarUserAvatar) {
                sidebarUserAvatar.src = `https://placehold.co/48x48/E2E8F0/475569?text=${currentUser.username.substring(0, 2).toUpperCase()}`;
                sidebarUserAvatar.alt = currentUser.username;
            }

            if (sidebarUsername) {
                sidebarUsername.textContent = currentUser.username;
            }
        }

        // --- Post Management ---
        async function loadPosts() {
            const postsContainer = document.getElementById('postsContainer');
            if (!postsContainer) return;

            try {
                postsContainer.innerHTML = '<div class="text-center py-8"><div class="spinner mx-auto mb-4"></div><p class="text-slate-500">Loading stories...</p></div>';

                const response = await fetch('/api/posts');
                const posts = await response.json();

                if (posts.length === 0) {
                    postsContainer.innerHTML = `
                        <div class="text-center py-12">
                            <i data-lucide="book-open" class="w-16 h-16 text-slate-300 mx-auto mb-4"></i>
                            <h3 class="text-xl font-semibold text-slate-600 mb-2">No stories yet</h3>
                            <p class="text-slate-500 mb-6">Be the first to share your positive experience!</p>
                            <button onclick="focusStoryCreation()" class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors">
                                Share Your Story
                            </button>
                        </div>
                    `;
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }
                    return;
                }

                postsContainer.innerHTML = posts.map(post => createPostHTML(post)).join('');

                // Initialize Lucide icons and attach event listeners
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
                attachPostListeners();

            } catch (error) {
                console.error('Error loading posts:', error);
                postsContainer.innerHTML = `
                    <div class="text-center py-8">
                        <i data-lucide="alert-circle" class="w-12 h-12 text-red-300 mx-auto mb-4"></i>
                        <p class="text-red-500">Failed to load stories. Please try again.</p>
                        <button onclick="loadPosts()" class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                            Retry
                        </button>
                    </div>
                `;
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }
        }

        function createPostHTML(post) {
            const timeAgo = getTimeAgo(new Date(post.createdAt));
            const userInitials = post.username.substring(0, 2).toUpperCase();

            return `
                <article class="bg-white rounded-xl shadow-sm border border-slate-200 p-6" data-story-title="${post.title}" data-story-content="${post.content}">
                    <div class="flex items-start space-x-4">
                        <img src="https://placehold.co/48x48/E2E8F0/475569?text=${userInitials}" alt="${post.username}" class="w-12 h-12 rounded-full flex-shrink-0">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h3 class="font-semibold text-slate-800">${post.username}</h3>
                                <span class="text-slate-500 text-sm">${timeAgo}</span>
                            </div>
                            <h2 class="text-xl font-bold text-slate-900 mb-3">${post.title}</h2>
                            <p class="text-slate-700 leading-relaxed mb-4">${post.content}</p>

                            <div class="flex items-center space-x-6 pt-4 border-t border-slate-100">
                                <button class="like-btn flex items-center space-x-2 text-slate-500 hover:text-red-500 transition-colors" data-post-id="${post.id}">
                                    <i data-lucide="heart" class="w-5 h-5"></i>
                                    <span>${post.likes || 0}</span>
                                </button>
                                <button class="comment-btn flex items-center space-x-2 text-slate-500 hover:text-indigo-500 transition-colors">
                                    <i data-lucide="message-circle" class="w-5 h-5"></i>
                                    <span>${post.comments || 0}</span>
                                </button>
                                <button class="share-btn flex items-center space-x-2 text-slate-500 hover:text-green-500 transition-colors">
                                    <i data-lucide="share" class="w-5 h-5"></i>
                                    <span>Share</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </article>
            `;
        }

        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

            return date.toLocaleDateString();
        }

        // Initialize Firebase and check authentication state
        async function initializeApp() {
            try {
                // Load Firebase configuration
                const firebaseModule = await import('/public/js/firebase-config.js');
                window.FirebaseAuth = firebaseModule.FirebaseAuth;

                // Initialize Firebase
                const initialized = await window.FirebaseAuth.init();
                if (!initialized) {
                    console.error('Failed to initialize Firebase');
                    return;
                }

                // Check if user is already authenticated
                window.FirebaseAuth.onAuthStateChanged((user) => {
                    if (user) {
                        // User is signed in, show main app
                        currentUser = {
                            uid: user.uid,
                            email: user.email,
                            username: user.username || user.email.split('@')[0],
                            displayName: user.displayName
                        };
                        showMainApp();
                        updateUIForUser();
                        loadPosts();
                        loadSuggestedUsers();
                    } else {
                        // User is signed out, show landing page
                        currentUser = null;
                        showLandingPage();
                    }
                });

            } catch (error) {
                console.error('Failed to initialize app:', error);
            }
        }

        // --- Post Interaction Functions ---
        function attachPostListeners() {
            // Attach like button listeners
            document.querySelectorAll('.like-btn').forEach(button => {
                button.addEventListener('click', async (e) => {
                    if (!currentUser) {
                        alert('Please sign in to like posts');
                        return;
                    }

                    const postId = button.dataset.postId;
                    const likeCount = button.querySelector('span');
                    const currentLikes = parseInt(likeCount.textContent) || 0;

                    try {
                        const response = await fetch(`/api/posts/${postId}/like`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ userId: currentUser.uid })
                        });

                        const result = await response.json();
                        if (result.success) {
                            likeCount.textContent = result.likes;
                            button.classList.toggle('text-red-500');
                        }
                    } catch (error) {
                        console.error('Error liking post:', error);
                    }
                });
            });

            // Attach comment button listeners
            document.querySelectorAll('.comment-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    const article = e.currentTarget.closest('article');
                    const title = article.dataset.storyTitle;
                    const content = article.dataset.storyContent;

                    openCommentModal(title, content);
                });
            });
        }

        function openCommentModal(title, content) {
            const modal = document.getElementById('commentModal');
            const modalTitle = document.getElementById('modalStoryTitle');

            if (modal && modalTitle) {
                modalTitle.textContent = title;
                modal.classList.remove('hidden');
            }
        }

        function focusStoryCreation() {
            const titleInput = document.getElementById('storyTitleInput') || document.getElementById('mobileStoryTitleInput');
            if (titleInput) {
                titleInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                titleInput.focus();
            }
        }

        // --- User Management ---
        async function loadSuggestedUsers() {
            const suggestedUsersContainer = document.getElementById('suggestedUsers');
            if (!suggestedUsersContainer) return;

            try {
                const response = await fetch('/api/users');
                const users = await response.json();

                // Filter out current user and limit to 3 suggestions
                const suggestions = users.filter(user => user.uid !== currentUser?.uid).slice(0, 3);

                if (suggestions.length === 0) {
                    suggestedUsersContainer.innerHTML = '<p class="text-slate-500 text-sm">No suggestions available</p>';
                    return;
                }

                suggestedUsersContainer.innerHTML = suggestions.map(user => `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <img src="https://placehold.co/32x32/E2E8F0/475569?text=${user.username.substring(0, 2).toUpperCase()}"
                                 alt="${user.username}" class="w-8 h-8 rounded-full">
                            <div>
                                <div class="font-medium text-sm">${user.username}</div>
                                <div class="text-xs text-slate-500">${user.followers || 0} followers</div>
                            </div>
                        </div>
                        <button class="follow-btn bg-indigo-600 text-white px-3 py-1 rounded text-xs hover:bg-indigo-700 transition-colors"
                                data-user-id="${user.uid}">
                            Follow
                        </button>
                    </div>
                `).join('');

                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            } catch (error) {
                console.error('Error loading suggested users:', error);
                suggestedUsersContainer.innerHTML = '<p class="text-red-500 text-sm">Failed to load suggestions</p>';
            }
        }

        // --- Mobile Navigation ---
        function updateMobileNavVisibility() {
            const mobileBottomNav = document.getElementById('mobileBottomNav');
            const isMainApp = !document.getElementById('mainApp').classList.contains('hidden');

            if (mobileBottomNav) {
                if (isMainApp) {
                    mobileBottomNav.classList.remove('hidden');
                } else {
                    mobileBottomNav.classList.add('hidden');
                }
            }
        }

        // --- Modal Functions ---
        function closeCommentModal() {
            const modal = document.getElementById('commentModal');
            if (modal) {
                modal.classList.add('hidden');
            }
        }

        function closeMessagesModal() {
            const modal = document.getElementById('messagesModal');
            if (modal) {
                modal.classList.add('hidden');
            }
        }

        function closeMobileSearch() {
            const modal = document.getElementById('mobileSearchModal');
            if (modal) {
                modal.classList.add('hidden');
            }
        }

        // --- User Actions ---
        function signOut() {
            if (window.FirebaseAuth) {
                window.FirebaseAuth.signOut();
            }
        }

        function showProfile() {
            alert('Profile functionality coming soon!');
        }

        function showSettings() {
            alert('Settings functionality coming soon!');
        }

        // Make functions globally available
        window.closeCommentModal = closeCommentModal;
        window.closeMessagesModal = closeMessagesModal;
        window.closeMobileSearch = closeMobileSearch;
        window.signOut = signOut;
        window.showProfile = showProfile;
        window.showSettings = showSettings;
        window.focusStoryCreation = focusStoryCreation;

        // Initialize the app when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Add click event listener as backup
            const websiteName = document.querySelector('.website-name');
            if (websiteName) {
                websiteName.addEventListener('click', function() {
                    openAuthModal();
                });
            }

            // Initialize main app event listeners
            initializeMainAppListeners();

            initializeApp();
        });

        function initializeMainAppListeners() {
            // Close comment modal
            const closeModalBtn = document.getElementById('closeModalBtn');
            if (closeModalBtn) {
                closeModalBtn.addEventListener('click', closeCommentModal);
            }

            // Close modal when clicking outside
            const commentModal = document.getElementById('commentModal');
            if (commentModal) {
                commentModal.addEventListener('click', (e) => {
                    if (e.target === commentModal) {
                        closeCommentModal();
                    }
                });
            }

            // Mobile navigation
            document.querySelectorAll('.mobile-nav-item').forEach(item => {
                item.addEventListener('click', (e) => {
                    const tab = e.currentTarget.getAttribute('data-tab');

                    // Update active state
                    document.querySelectorAll('.mobile-nav-item').forEach(navItem =>
                        navItem.classList.remove('active'));
                    e.currentTarget.classList.add('active');

                    // Handle navigation
                    switch(tab) {
                        case 'home':
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                            break;
                        case 'search':
                            const mobileSearchModal = document.getElementById('mobileSearchModal');
                            if (mobileSearchModal) {
                                mobileSearchModal.classList.remove('hidden');
                                const searchInput = document.getElementById('mobileSearchInput');
                                if (searchInput) {
                                    setTimeout(() => searchInput.focus(), 100);
                                }
                            }
                            break;
                        case 'create':
                            if (currentUser) {
                                focusStoryCreation();
                            } else {
                                openAuthModal();
                            }
                            break;
                        case 'notifications':
                            if (currentUser) {
                                alert('Notifications coming soon!');
                            } else {
                                openAuthModal();
                            }
                            break;
                        case 'profile':
                            if (currentUser) {
                                showProfile();
                            } else {
                                openAuthModal();
                            }
                            break;
                    }
                });
            });

            // Story form submissions
            const storyForm = document.getElementById('storyForm');
            const mobileStoryForm = document.getElementById('mobileStoryForm');

            if (storyForm) {
                storyForm.addEventListener('submit', handleStorySubmission);
            }

            if (mobileStoryForm) {
                mobileStoryForm.addEventListener('submit', handleMobileStorySubmission);
            }

            // Refresh feed button
            const refreshFeedBtn = document.getElementById('refreshFeedBtn');
            if (refreshFeedBtn) {
                refreshFeedBtn.addEventListener('click', loadPosts);
            }

            // Load more button
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            if (loadMoreBtn) {
                loadMoreBtn.addEventListener('click', () => {
                    alert('Load more functionality coming soon!');
                });
            }
        }

        async function handleStorySubmission(e) {
            e.preventDefault();

            if (!currentUser) {
                openAuthModal();
                return;
            }

            const title = document.getElementById('storyTitleInput').value.trim();
            const content = document.getElementById('storyContentInput').value.trim();

            if (!title || !content) {
                alert('Please fill in both title and content');
                return;
            }

            try {
                const response = await fetch('/api/posts', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        title,
                        content,
                        userId: currentUser.uid,
                        username: currentUser.username
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Clear form
                    document.getElementById('storyTitleInput').value = '';
                    document.getElementById('storyContentInput').value = '';

                    // Reload posts
                    loadPosts();

                    alert('Story shared successfully!');
                } else {
                    alert('Failed to share story: ' + (result.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error sharing story:', error);
                alert('Failed to share story. Please try again.');
            }
        }

        async function handleMobileStorySubmission(e) {
            e.preventDefault();

            if (!currentUser) {
                openAuthModal();
                return;
            }

            const title = document.getElementById('mobileStoryTitleInput').value.trim();
            const content = document.getElementById('mobileStoryContentInput').value.trim();

            if (!title || !content) {
                alert('Please fill in both title and content');
                return;
            }

            try {
                const response = await fetch('/api/posts', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        title,
                        content,
                        userId: currentUser.uid,
                        username: currentUser.username
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Clear form
                    document.getElementById('mobileStoryTitleInput').value = '';
                    document.getElementById('mobileStoryContentInput').value = '';

                    // Reload posts
                    loadPosts();

                    alert('Story shared successfully!');
                } else {
                    alert('Failed to share story: ' + (result.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error sharing story:', error);
                alert('Failed to share story. Please try again.');
            }
        }
    </script>
</body>
</html>